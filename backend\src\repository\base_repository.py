from contextlib import AbstractAsync<PERSON>ontextManager
from typing import Callable
from uuid import UUID

from pydantic import BaseModel
from sqlalchemy.exc import NoResultFound
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, update

from models.base import Base


class BaseRepository[Model: Base, CreateSchema: BaseModel, UpdateSchema: BaseModel]:
    def __init__(self,
                 session_factory: Callable[[], AbstractAsyncContextManager[AsyncSession]], model_class: type[Model]):
        self.session_factory = session_factory
        self.model_class = model_class

    async def get_all(self):
        async with self.session_factory() as session:
            stmt = select(self.model_class)
            records = await session.execute(stmt)
            return records.scalars().all()

    async def get(self, id: UUID):
        async with self.session_factory() as session:
            async with session.begin_nested():
                stmt = select(self.model_class).where(self.model_class.id == id).with_for_update()
                record = await session.execute(stmt)
                record = record.scalars().first()
                if record is None:
                    raise NoResultFound

                return record

    async def create(self, data: CreateSchema):
        async with self.session_factory() as session:
            async with session.begin():
                db_obj = self.model_class(**data.model_dump())
                session.add(db_obj)
                await session.flush()
                return db_obj

    async def update(self, id: UUID, data: UpdateSchema):
        async with self.session_factory() as session:
            async with session.begin():
                stmt_select = select(self.model_class).where(self.model_class.id == id)
                result = await session.execute(stmt_select)
                obj = result.scalars().one_or_none()

                if obj is None:
                    raise NoResultFound

                stmt_update = update(self.model_class).where(
                    self.model_class.id == id
                ).values(
                    data.model_dump(exclude_unset=True, exclude_none=True)
                )
                await session.execute(stmt_update)

                await session.refresh(obj)
                await session.flush()
                return obj

    async def delete(self, id: UUID):
        async with self.session_factory() as session:
            async with session.begin():
                stmt = select(self.model_class).where(self.model_class.id == id).with_for_update()
                record = await session.execute(stmt)
                record = record.scalars().one_or_none()

                if record is None:
                    raise NoResultFound

                await session.delete(record)

    async def count(self):
        async with self.session_factory() as session:
            stmt = select(func.count()).select_from(self.model_class)

            result = await session.execute(stmt)
            return result.scalar()
