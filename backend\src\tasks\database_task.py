import asyncio

from decimal import Decimal

from sqlalchemy.exc import NoResultFound

from core.celery import app
from models.enum import ChangeReason
from schemas.coefficient_log import CoefficientLogInDB
from tasks.container_tasks import TaskContainer
from schemas.menu_item import MenuItemUpdateInDB
from repository import MenuItemRepository, CoefficientLogRepository


@app.task
def update_item_coefficient():
    container = TaskContainer()
    container.init_resources()
    menu_item_repository: MenuItemRepository = container.menu_item_repository()
    coefficient_log_repository: CoefficientLogRepository = container.coefficient_log_repository()

    async def run():
        try:
            menu_items = await menu_item_repository.get_available()

            for menu_item in menu_items:
                previous_coefficient = menu_item.coefficient

                new_coefficient = menu_item.coefficient - Decimal("0.01")

                if new_coefficient < Decimal('0.8'):
                    print(f"Skipping update for item {menu_item.id}: coefficient would go below 0.8")
                    continue

                await menu_item_repository.update(menu_item.id, MenuItemUpdateInDB(coefficient=new_coefficient))

                coefficient_log = CoefficientLogInDB(
                    item_id=menu_item.id,
                    previous_coefficient=previous_coefficient,
                    new_coefficient=new_coefficient,
                    change_reason=ChangeReason.DECAYED
                )

                await coefficient_log_repository.create(coefficient_log)


        except NoResultFound:
            raise Exception("Item not found")
        except Exception:
            raise Exception("Internal server error")

    asyncio.run(run())
