import uuid
from datetime import datetime
from decimal import Decimal
from typing import List, Optional

from sqlalchemy import UUI<PERSON>, Float, Numeric, String, CheckConstraint, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.ext.hybrid import hybrid_property

from .base import Base


class MenuItem(Base):
    __tablename__ = "menu_items"
    __table_args__ = (
        CheckConstraint('coefficient >= 0.8 AND coefficient <= 2.0', name='check_coefficient_range'),
    )

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    category_id: Mapped[UUID] = mapped_column(ForeignKey("categories.id"), nullable=False)
    base_price: Mapped[Decimal] = mapped_column(Numeric(10, 2), nullable=False)
    coefficient: Mapped[Decimal] = mapped_column(
        Numeric(10, 2),
        nullable=False,
        default=1.0
    )
    available: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    deleted_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    category: Mapped["Category"] = relationship(back_populates="menu_items")
    coefficient_logs: Mapped[List["CoefficientLog"]] = relationship(back_populates="menu_item")
    order_items: Mapped[List["OrderItem"]] = relationship(back_populates="menu_item")

    @hybrid_property
    def final_price(self) -> Decimal:
        result = self.base_price * self.coefficient
        return round(result, 2)

    @final_price.expression
    def final_price(cls):
        return cls.base_price * cls.coefficient
