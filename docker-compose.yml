services:
  backend:
    build: ./backend
    env_file:
      - .env
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    networks:
      - app_network
    restart: on-failure:3

  db:
    image: postgres:16
    env_file:
      - .env
    volumes:
      - beverates:/var/lib/postgresql/data/
    ports:
      - "5432:5432"
    restart: on-failure:3
    networks:
      - app_network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - app_network
    restart: on-failure:3

  celery-worker:
    build: ./backend
    env_file:
      - .env
    command: celery -A core.celery worker --loglevel=info
    depends_on:
      - backend
      - redis
    networks:
      - app_network
    restart: on-failure:3

  celery-beat:
    build: ./backend
    env_file:
      - .env
    command: celery -A core.celery beat --loglevel=info
    depends_on:
      - backend
      - redis
    networks:
      - app_network
    restart: on-failure:3

networks:
  app_network:
    driver: bridge

volumes:
  beverates:
