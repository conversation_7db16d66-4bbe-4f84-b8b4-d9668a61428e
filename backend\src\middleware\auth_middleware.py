from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import Request, Response
from starlette.responses import Response as StarletteResponse
from typing import Callable

from core.security import create_tokens


class TokenRefreshMiddleware(BaseHTTPMiddleware):
    def __init__(self, app):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next: Callable) -> StarletteResponse:
        response = await call_next(request)

        if hasattr(request.state, "needs_token_refresh") and request.state.needs_token_refresh:
            user_id = request.state.user_id

            new_access_token, new_refresh_token = create_tokens(user_id)

            self._set_auth_cookies(response, new_access_token, new_refresh_token)

        return response

    def _set_auth_cookies(self, response: Response, access_token: str, refresh_token: str) -> None:
        if hasattr(response, "set_cookie"):
            response.set_cookie(
                key="access_token",
                value=access_token,
                httponly=True,
                max_age=1800,
                samesite="lax"
            )
            response.set_cookie(
                key="refresh_token",
                value=refresh_token,
                httponly=True,
                max_age=604800,
                samesite="strict"
            )
        else:
            pass
