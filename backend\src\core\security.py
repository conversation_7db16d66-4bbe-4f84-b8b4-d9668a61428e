import logging
import uuid
from datetime import datetime, timedelta, timezone
from typing import Dict, <PERSON><PERSON>
from uuid import UUID

from jose import JW<PERSON>rror, jwt, ExpiredSignatureError
from passlib.context import CryptContext

from core.config import settings

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def verify_password(plain_password, hashed_password) -> bool:
    return pwd_context.verify(plain_password, hashed_password)


def hash_password(password) -> str:
    return pwd_context.hash(password)


def create_token(data: Dict, expires_delta: timedelta) -> str:
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + expires_delta
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, settings.SECRET_KEY, settings.ALGORITHM)


def create_tokens(id: UUID) -> Tuple[str, str]:
    now = datetime.now(timezone.utc)
    access_token = create_token(data={
        "sub": str(id),
        "type": "access",
        "iat": int(now.timestamp()),
        "jti": str(uuid.uuid4()),
    }, expires_delta=timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES))
    refresh_token = create_token(data={
        "sub": str(id),
        "type": "refresh",
        "iat": int(now.timestamp()),
        "jti": str(uuid.uuid4()),
    }, expires_delta=timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS))

    return access_token, refresh_token


def decode_token(token: str) -> Dict | None:
    try:
        if not token:
            return None

        if token.count('.') != 2:
            return None

        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        return payload
    except ExpiredSignatureError:
        return None
    except JWTError:
        logging.debug("JWT decode error - invalid token format or signature")
        return None
    except Exception as e:
        logging.error(f"Unexpected error decoding JWT: {str(e)}")
        return None
