@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Modern Light Theme */
  --background: #f8fafc;
  --foreground: #0f172a;
  --card: #ffffff;
  --card-foreground: #1e293b;
  --popover: #ffffff;
  --popover-foreground: #1e293b;
  --primary: #6366f1;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #334155;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: #818cf8;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #6366f1;

  /* Stock Change Colors - Light Mode - Traditional Stock Market */
  --price-up: #22c55e; /* Bright green for gains */
  --price-down: #ef4444; /* Bright red for losses */
  --price-neutral: #eab308; /* Bright yellow for neutral/static text */
  --price-value: #1e293b; /* Dark text for price values */
  --stock-header-bg: #dc2626; /* Red header background */
  --stock-header-text: #fbbf24; /* Yellow header text */
  --stock-table-bg: #f8fafc; /* Light background for table */
  --stock-row-hover: #f1f5f9; /* Light hover effect */

  /* Chart Colors */
  --chart-1: #6366f1;
  --chart-2: #8b5cf6;
  --chart-3: #ec4899;
  --chart-4: #f43f5e;
  --chart-5: #f97316;

  /* Sidebar */
  --sidebar: #f8fafc;
  --sidebar-foreground: #0f172a;
  --sidebar-primary: #6366f1;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #e0e7ff;
  --sidebar-accent-foreground: #1e1b4b;
  --sidebar-border: #e2e8f0;
  --sidebar-ring: #6366f1;

  /* Fonts */
  --font-sans: "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, sans-serif;
  --font-serif: "Georgia", "Times New Roman", serif;
  --font-mono: "JetBrains Mono", "Courier New", monospace;

  /* Radius and Shadows */
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-xs: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-sm: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-md: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-xl: 0 25px 50px -12px rgb(0 0 0 / 0.25), 0 0 15px 0 rgb(0 0 0 / 0.05);
  --shadow-2xl: 0 35px 60px -15px rgb(0 0 0 / 0.3);
}

.dark {
  /* Modern Dark Theme */
  --background: #0f172a;
  --foreground: #f8fafc;
  --card: #1e293b;
  --card-foreground: #f1f5f9;
  --popover: #1e293b;
  --popover-foreground: #f1f5f9;
  --primary: #818cf8;
  --primary-foreground: #ffffff;
  --secondary: #334155;
  --secondary-foreground: #f1f5f9;
  --muted: #1e293b;
  --muted-foreground: #94a3b8;
  --accent: #4f46e5;
  --accent-foreground: #e0e7ff;
  --destructive: #f87171;
  --destructive-foreground: #ffffff;
  --border: #334155;
  --input: #334155;
  --ring: #818cf8;

  /* Stock Change Colors - Dark Mode - Traditional Stock Market */
  --price-up: #22c55e; /* Bright green for gains */
  --price-down: #ef4444; /* Bright red for losses */
  --price-neutral: #eab308; /* Bright yellow for neutral/static text */
  --price-value: #f1f5f9; /* Light text for price values */
  --stock-header-bg: #dc2626; /* Red header background */
  --stock-header-text: #fbbf24; /* Yellow header text */
  --stock-table-bg: #1e293b; /* Dark background for table */
  --stock-row-hover: #334155; /* Dark hover effect */

  /* Chart Colors */
  --chart-1: #818cf8;
  --chart-2: #a78bfa;
  --chart-3: #f472b6;
  --chart-4: #fb7185;
  --chart-5: #fb923c;

  /* Sidebar */
  --sidebar: #0f172a;
  --sidebar-foreground: #f8fafc;
  --sidebar-primary: #818cf8;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #4f46e5;
  --sidebar-accent-foreground: #e0e7ff;
  --sidebar-border: #334155;
  --sidebar-ring: #818cf8;

  /* Fonts */
  --font-sans: "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, sans-serif;
  --font-serif: "Georgia", "Times New Roman", serif;
  --font-mono: "JetBrains Mono", "Courier New", monospace;

  /* Radius and Shadows */
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-xs: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
  --shadow-sm: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
  --shadow: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
  --shadow-md: 0 20px 25px -5px rgb(0 0 0 / 0.4),
    0 8px 10px -6px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 25px 50px -12px rgb(0 0 0 / 0.5);
  --shadow-xl: 0 25px 50px -12px rgb(0 0 0 / 0.5), 0 0 15px 0 rgb(0 0 0 / 0.2);
  --shadow-2xl: 0 35px 60px -15px rgb(0 0 0 / 0.6);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Better focus styles */
  :focus-visible {
    @apply outline-2 outline-offset-2 outline-primary;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/40 rounded-full hover:bg-muted-foreground/60 transition-colors;
  }

  /* Headings */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-medium tracking-tight;
  }

  h1 {
    @apply text-3xl sm:text-4xl;
  }

  h2 {
    @apply text-2xl sm:text-3xl;
  }

  h3 {
    @apply text-xl sm:text-2xl;
  }

  /* Links */
  a {
    @apply transition-colors;
  }
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Animation utility classes */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

.animate-slide-in-up {
  animation: slideInUp 0.4s ease-out forwards;
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Card hover effects */
.card-hover {
  @apply transition-all duration-300 hover:shadow-md hover:-translate-y-1;
}

/* Glass effect */
.glass {
  @apply bg-background/80 backdrop-blur-md border border-border/50;
}

/* Modern button styles */
.btn-modern {
  @apply relative overflow-hidden transition-all duration-300
    before:absolute before:inset-0 before:bg-primary/10 before:scale-x-0
    before:origin-right before:transition-transform before:duration-300
    hover:before:scale-x-100 hover:before:origin-left;
}

/* Responsive container */
.container-responsive {
  @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Stock Change Colors */
.text-price-up {
  color: var(--price-up);
}

.text-price-down {
  color: var(--price-down);
}

.text-price-neutral {
  color: var(--price-neutral);
}

.text-price-value {
  color: var(--price-value);
}

/* Fullscreen table transitions */
.table-fullscreen-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.table-expand-animation {
  animation: expandTable 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.table-minimize-animation {
  animation: minimizeTable 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes expandTable {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(1.02);
    opacity: 1;
  }
}

@keyframes minimizeTable {
  from {
    transform: scale(1.02);
    opacity: 1;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Traditional Stock Market Triangle Arrows */
.triangle-up {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 7px solid var(--price-up);
  display: inline-block;
  vertical-align: middle;
}

.triangle-down {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 7px solid var(--price-down);
  display: inline-block;
  vertical-align: middle;
}

.triangle-neutral {
  width: 8px;
  height: 1px;
  background-color: var(--price-neutral);
  display: inline-block;
  vertical-align: middle;
}

/* Stock Market Header Styling */
.stock-header {
  background-color: var(--stock-header-bg);
  color: var(--stock-header-text);
}

.stock-table-bg {
  background-color: var(--stock-table-bg);
  color: var(--price-value);
}

.stock-row-hover:hover {
  background-color: var(--stock-row-hover);
}

/* Custom scrollbar for stock table */
.stock-table-scroll::-webkit-scrollbar {
  height: 6px;
}

.stock-table-scroll::-webkit-scrollbar-track {
  background: var(--stock-table-bg);
  border-radius: 3px;
}

.stock-table-scroll::-webkit-scrollbar-thumb {
  background: var(--price-neutral);
  border-radius: 3px;
}

.stock-table-scroll::-webkit-scrollbar-thumb:hover {
  background: var(--stock-header-bg);
}

/* Ticker Tape Styles */
.ticker-tape {
  background: var(--card);
  border-top: 1px solid var(--border);
  border-bottom: 1px solid var(--border);
  position: relative;
  overflow: hidden;
}

.ticker-tape::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 50px;
  height: 100%;
  background: linear-gradient(to right, var(--card), transparent);
  z-index: 10;
  pointer-events: none;
}

.ticker-tape::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 50px;
  height: 100%;
  background: linear-gradient(to left, var(--card), transparent);
  z-index: 10;
  pointer-events: none;
}

.ticker-item {
  display: flex;
  align-items: center;
  white-space: nowrap;
  padding: 0 2rem;
  font-family: "Courier New", monospace;
}

.ticker-separator {
  color: var(--muted-foreground);
  margin: 0 1rem;
}

/* Ticker animation */
@keyframes tickerScroll {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}
