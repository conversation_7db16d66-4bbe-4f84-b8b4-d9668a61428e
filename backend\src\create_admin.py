import asyncio

from dependency_injector.wiring import Provide, inject

from core.config import settings
from core.di import Container
from core.security import hash_password
from repository import UserRepository
from schemas.user import UserInDB


@inject
async def create_admin(user_repository: UserRepository = Provide[Container.user_repository]):
    user = await user_repository.get_by_email(email=settings.ADMIN_EMAIL)

    if not user:
        user = await user_repository.create(
            UserInDB(email=settings.ADMIN_EMAIL, hash_password=hash_password(settings.ADMIN_PASSWORD), is_admin=True))
        print("Admin created", user.email)
    else:
        print("Admin already exists", user.email)


if __name__ == '__main__':
    container = Container()
    container.init_resources()
    container.wire(modules=[__name__])
    asyncio.run(create_admin())
