from uuid import UUID

from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.exc import NoResultFound
from sqlalchemy.orm import joinedload

from models.order import Order
from repository.base_repository import BaseRepository


class OrderRepository(BaseRepository[Order, BaseModel, BaseModel]):
    def __init__(self, session_factory):
        super().__init__(session_factory, Order)

    async def get(self, order_id: UUID):
        async with self.session_factory() as session:
            async with session.begin_nested():
                stmt = select(self.model_class).where(self.model_class.id == order_id).options(
                    joinedload(self.model_class.items)
                )
                record = await session.execute(stmt)
                record = record.scalars().first()
                if record is None:
                    raise NoResultFound

                return record

    async def get_all(self, pagination: None):
        async with self.session_factory() as session:
            stmt = select(self.model_class).options(
                joinedload(self.model_class.items)
            )

            if pagination:
                stmt = stmt.offset(pagination.offset).limit(pagination.page_size)

            records = await session.execute(stmt)
            records = records.unique().scalars().all()
            if records is None:
                raise NoResultFound

            return records
