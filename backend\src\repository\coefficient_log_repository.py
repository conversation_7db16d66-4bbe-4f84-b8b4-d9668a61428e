from uuid import UUID

from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.exc import NoResultFound
from sqlalchemy.orm import joinedload

from models import MenuItem
from models.coefficient_log import CoefficientLog
from repository.base_repository import BaseRepository


class CoefficientLogRepository(BaseRepository[CoefficientLog, BaseModel, BaseModel]):
    def __init__(self, session_factory):
        super().__init__(session_factory, CoefficientLog)

    async def get(self, item_id: UUID):
        async with self.session_factory() as session:
            stmt = select(self.model_class).where(self.model_class.item_id == item_id).options(
                joinedload(self.model_class.menu_item).joinedload(MenuItem.category)
            ).order_by(self.model_class.timestamp.desc())
            record = await session.execute(stmt)
            record = record.scalars().first()
            if record is None:
                raise NoResultFound

            return record

    async def get_all(self, item_id: UUID):
        async with self.session_factory() as session:
            stmt = select(self.model_class).where(self.model_class.item_id == item_id).options(
                joinedload(self.model_class.menu_item).joinedload(MenuItem.category)
            ).order_by(self.model_class.timestamp.asc())
            record = await session.execute(stmt)
            record = record.scalars().all()
            if record is None:
                raise NoResultFound

            return record
