import asyncio
import json
from unicodedata import category
from uuid import <PERSON><PERSON><PERSON>

from fastapi import status, HTTPException, Request
from fastapi.responses import JSONResponse
from sqlalchemy.exc import NoResultFound, IntegrityError

from models.enum import ChangeReason
from models.menuitem import MenuItem
from repository import CoefficientLogRepository
from repository.menu_item_repository import MenuItemRepository
from schemas.coefficient_log import CoefficientLogInDB
from schemas.menu_item import MenuItemCreate, MenuItemUpdate, MenuItemInDB, MenuItemUpdateInDB, MenuItemResponse
from schemas.pagination import PaginationResponse
from services.base_service import BaseService


class MenuItemService(BaseService[MenuItem, MenuItemInDB, MenuItemUpdateInDB, MenuItemRepository]):
    def __init__(self, menu_item_repository: MenuItemRepository,
                 coefficient_log_repository: CoefficientLogRepository, ):
        super().__init__(menu_item_repository)
        self._menu_item_repository = menu_item_repository
        self._coefficient_log_repository = coefficient_log_repository

    async def get_all(self, **filters):
        try:
            pagination = filters.pop("pagination", None)
            category_id = filters.pop("category_id", None)

            menu_items = await self._menu_item_repository.get_available(pagination, category_id)

            total_items = await self._menu_item_repository.count(category_id)

            total_pages = (total_items + pagination.page_size - 1) // pagination.page_size

            return PaginationResponse(
                items=menu_items,
                total=total_items,
                page=pagination.page,
                page_size=pagination.page_size,
                pages=total_pages,
            )
        except Exception as e:
            print("Exception while getting all menu items", e)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error"
            )

    async def get(self, id: UUID):
        try:
            return await self._menu_item_repository.get_available_by_id(id)
        except NoResultFound:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail=f"Object with id {id} not found",
            )
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error",
            )

    async def create_menu_item(self, menu_item: MenuItemCreate):
        item = MenuItemInDB(
            name=menu_item.name, category_id=menu_item.category_id, base_price=menu_item.base_price
        )

        db_item = await self.repository.create(item)
        await self._coefficient_log_repository.create(CoefficientLogInDB(
            item_id=db_item.id, previous_coefficient=db_item.coefficient, new_coefficient=db_item.coefficient,
            change_reason=ChangeReason.CREATED
        ))
        return JSONResponse(status_code=status.HTTP_201_CREATED, content="created")

    async def update_menu_item(self, item_id: UUID, menu_item: MenuItemUpdate):
        try:
            if menu_item.coefficient:
                item = await self.repository.get(item_id)
                await self._coefficient_log_repository.create(
                    CoefficientLogInDB(item_id=item_id, previous_coefficient=item.coefficient,
                                       new_coefficient=menu_item.coefficient, change_reason=ChangeReason.MANUAL_UPDATE)
                )

            db_item = MenuItemUpdateInDB(**menu_item.model_dump())
            return await self.repository.update(item_id, db_item)
        except NoResultFound:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Menu item not found"
            )
        except IntegrityError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Coefficient should be between 0.8 and 2.0"
            )
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error"
            )

    async def delete_menu_item(self, item_id: UUID):
        try:
            return await self._menu_item_repository.soft_delete(item_id)
        except NoResultFound:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Order not found"
            )
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error"
            )

    async def update_coefficient_by_category(self, category_id: UUID, coefficient: float):
        try:
            items = await self._menu_item_repository.get_available_by_category_id(category_id)
            if not items:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="No items found for this category"
                )
            response = []
            for item in items:
                response.append(
                    await self._menu_item_repository.update(item.id, MenuItemUpdateInDB(coefficient=coefficient)))
                await self._coefficient_log_repository.create(
                    CoefficientLogInDB(item_id=item.id, previous_coefficient=item.coefficient,
                                       new_coefficient=coefficient, change_reason=ChangeReason.MANUAL_UPDATE)
                )
            return response

        except NoResultFound:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Category not found"
            )
        except IntegrityError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Coefficient should be between 0.8 and 2.0"
            )
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error"
            )

    async def watch(self):
        menu_items = await self.get_all()
        yield menu_items

        while True:
            await asyncio.sleep(5)

            updated_items = await self.get_all()
            yield updated_items

    async def event_generator(self, request: Request):
        try:
            async for items in self.watch():
                if await request.is_disconnected():
                    break

                json_data = json.dumps([MenuItemResponse.model_validate(item).model_dump() for item in items],
                                       default=str)

                yield f"data: {json_data}\n\n"

        except Exception as e:
            print(f"Error in SSE stream: {str(e)}")
            # need to change for prod
            yield f"event: error\ndata: {json.dumps({'error': str(e)})}\n\n"
