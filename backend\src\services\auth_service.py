from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Request, Response, status
from jose import <PERSON><PERSON><PERSON><PERSON><PERSON>
from pydantic import BaseModel
from sqlalchemy.exc import NoResultFound

from core.security import create_tokens, decode_token, verify_password
from models.user import User
from repository.user_repository import UserRepository
from schemas.auth import LoginRequest
from services.base_service import BaseService


class AuthService(BaseService[User, BaseModel, BaseModel, UserRepository]):
    def __init__(self, user_repository: UserRepository):
        self.user_repository = user_repository
        super().__init__(user_repository)

    async def login(self, sign_in_data: LoginRequest, response: Response):
        user = await self.user_repository.get_by_email(sign_in_data.email)
        if not user or not verify_password(sign_in_data.password, user.hash_password):
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Incorrect username or password")

        access_token, refresh_token = create_tokens(user.id)
        response.set_cookie(
            key="access_token",
            value=access_token,
            httponly=True,
            max_age=1800,
            samesite="lax"
        )

        response.set_cookie(
            key="refresh_token",
            value=refresh_token,
            httponly=True,
            max_age=604800,
            samesite="strict"
        )
        return {"message": "Successfully logged in", "user_id": user.id}

    async def refresh(self, request: Request, response: Response):
        token = request.cookies.get("refresh_token")
        if not token:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Refresh token missing")

        payload = decode_token(token)
        if not payload:
            response.delete_cookie(key="access_token")
            response.delete_cookie(key="refresh_token")
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid refresh token")

        user_id = payload.get("sub")
        if not user_id:
            response.delete_cookie(key="access_token")
            response.delete_cookie(key="refresh_token")
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")

        try:
            user = await self.repository.get(user_id)
            if not user:
                response.delete_cookie(key="access_token")
                response.delete_cookie(key="refresh_token")
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="User not found")

            new_access_token, new_refresh_token = create_tokens(user.id)
            response.set_cookie(
                key="access_token",
                value=new_access_token,
                httponly=True,
                max_age=1800,
                samesite="lax"
            )

            response.set_cookie(
                key="refresh_token",
                value=new_refresh_token,
                httponly=True,
                max_age=604800,
                samesite="strict"
            )

            return {"message": "Tokens refreshed successfully"}
        except NoResultFound:
            response.delete_cookie(key="access_token")
            response.delete_cookie(key="refresh_token")
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

    async def logout(self, request: Request, response: Response):
        if not request.cookies.get("refresh_token") and not request.cookies.get("access_token"):
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Already logged out or not logged in")

        response.delete_cookie(key="access_token")
        response.delete_cookie(key="refresh_token")

        return {"message": "Successfully logged out"}