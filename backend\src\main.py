from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from api.main import api_router
from core.di import Container
from middleware.auth_middleware import TokenRefreshMiddleware


@asynccontextmanager
async def lifespan(app: FastAPI):
    db = app.container.database()
    await db.init_db()

    yield


app = FastAPI(
    lifespan=lifespan,
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
app.add_middleware(TokenRefreshMiddleware)

app.container = Container()
app.include_router(api_router)
