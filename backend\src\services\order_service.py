from decimal import Decimal
from uuid import UUID

from fastapi import status, HTTPException
from sqlalchemy.exc import NoResultFound

from models.enum import ChangeReason
from models.order import Order
from repository import MenuItemRepository, CoefficientLogRepository
from repository.order_repository import OrderRepository
from repository.order_item_repository import OrderItemRepository
from schemas.coefficient_log import CoefficientLogInDB
from schemas.menu_item import MenuItemUpdateInDB
from schemas.order import OrderCreate, OrderInDB, OrderInDBUpdate, OrderUpdate
from schemas.order_item import OrderItemInDB
from schemas.pagination import PaginationResponse
from services.base_service import BaseService


class OrderService(BaseService[Order, OrderCreate, OrderInDBUpdate, OrderRepository]):
    def __init__(self, order_repository: OrderRepository, order_item_repository: OrderItemRepository,
                 menu_item_repository: MenuItemRepository, coefficient_log_repository: CoefficientLogRepository):
        super().__init__(order_repository)
        self.order_repository = order_repository
        self.menu_item_repository = menu_item_repository
        self.order_item_repository = order_item_repository
        self.coefficient_log_repository = coefficient_log_repository

    async def create_order(self, order: OrderCreate):
        new_order = OrderInDB(
            total_price=Decimal("0.00"),
            items=[]
        )

        db_order = await self.order_repository.create(new_order)

        total_price = Decimal('0.00')

        for item in order.items:
            try:
                menu_item = await self.menu_item_repository.get(item.menu_item_id)
            except NoResultFound:
                return HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Item not found")

            final_price = menu_item.final_price
            item_subtotal = final_price * Decimal(item.quantity)
            total_price += item_subtotal

            await self.order_item_repository.create(OrderItemInDB(
                order_id=db_order.id,
                menu_item_id=menu_item.id,
                price_at_order=final_price,
                quantity=item.quantity,
            ))

            previous_coefficient = menu_item.coefficient
            new_coefficient = previous_coefficient + Decimal("0.05")

            await self.coefficient_log_repository.create(
                CoefficientLogInDB(item_id=menu_item.id, previous_coefficient=previous_coefficient,
                                   new_coefficient=new_coefficient, change_reason=ChangeReason.ORDERED))

            await self.menu_item_repository.update(menu_item.id, MenuItemUpdateInDB(coefficient=new_coefficient))

        data = await self.order_repository.update(db_order.id, OrderInDBUpdate(total_price=total_price))

        return data

    async def get_order(self, order_id: UUID):
        try:
            return await self.order_repository.get(order_id)
        except NoResultFound:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Order not found")
        except Exception:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")

    async def get_orders(self, **filters):
        try:
            pagination = filters.pop("pagination", None)

            orders = await self.order_repository.get_all(pagination)

            total_items = await self.repository.count()

            total_pages = (total_items + pagination.page_size - 1) // pagination.page_size

            return PaginationResponse(
                items=orders,
                total=total_items,
                page=pagination.page,
                page_size=pagination.page_size,
                pages=total_pages
            )
        except Exception:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")

    async def update_order_status(self, order_id: UUID, order_status: OrderUpdate):
        try:
            return await self.order_repository.update(order_id, order_status)
        except NoResultFound:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Order not found")
        except Exception:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")
