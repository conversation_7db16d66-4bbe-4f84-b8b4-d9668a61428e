[tool.poetry]
name = "fastapiproject"
version = "0.1.0"
description = ""
authors = ["VoiceOfDarkness <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.12"
fastapi = "^0.115.7"
uvicorn = "^0.34.0"
pydantic-settings = "^2.7.1"
isort = "^5.13.2"
sqlalchemy = "^2.0.37"
asyncpg = "^0.30.0"
dependency-injector = "^4.45.0"
passlib = "^1.7.4"
python-jose = "^3.3.0"
python-multipart = "^0.0.20"
typer = "^0.15.1"
email-validator = "^2.2.0"
celery = "^5.5.1"
redis = "^5.2.1"
asyncio = "^3.4.3"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
