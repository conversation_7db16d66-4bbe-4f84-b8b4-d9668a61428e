from decimal import Decimal
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field


class OrderItemBase(BaseModel):
    model_config = ConfigDict(from_attributes=True)


class OrderItemCreate(BaseModel):
    menu_item_id: UUID
    quantity: int = Field(1, ge=1, description="Quantity of items to order")


class OrderItemInDB(OrderItemCreate):
    order_id: UUID
    price_at_order: Decimal


class OrderItemResponse(OrderItemBase):
    id: UUID
    order_id: UUID
    menu_item_id: UUID
    price_at_order: Decimal
    quantity: int


class OrderItemUpdate(BaseModel):
    quantity: Optional[int] = None
