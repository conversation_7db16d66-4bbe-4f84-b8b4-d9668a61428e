import uuid
from datetime import datetime
from decimal import Decimal
from typing import Annotated, List

from sqlalchemy import Numeric, UUID, func, DateTime, Enum
from sqlalchemy.orm import mapped_column, Mapped, relationship

from .base import Base
from models.enum import Status


class Order(Base):
    __tablename__ = "orders"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    total_price: Mapped[Decimal] = mapped_column(Numeric(10, 2), nullable=False)
    status: Mapped[Annotated[Status, "order_status"]] = mapped_column(Enum(Status), default=Status.RECEIVED,
                                                                      nullable=False)
    timestamp: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, default=func.now())

    items: Mapped[List["OrderItem"]] = relationship(back_populates="order")
