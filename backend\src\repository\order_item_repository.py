from typing import List
from uuid import UUID

from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.exc import NoResultFound

from models.order_item import OrderItem
from repository.base_repository import BaseRepository


class OrderItemRepository(BaseRepository[OrderItem, BaseModel, BaseModel]):
    def __init__(self, session_factory):
        super().__init__(session_factory, OrderItem)

    async def get_by_menu_item_id(self, menu_item_id: List[UUID]):
        menu_items = []

        for menu_item_id in menu_item_id:
            async with self.session_factory() as session:
                stmt = select(self.model_class).where(self.model_class.menu_item_id == menu_item_id)
                record = await session.execute(stmt)
                record = record.scalars().first()
                if record is None:
                    raise NoResultFound

                menu_items.append(record.as_dict())

        return menu_items
