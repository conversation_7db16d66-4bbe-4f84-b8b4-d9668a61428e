from decimal import Decimal
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field

from schemas.category import CategoryResponse


class MenuItemBase(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    name: str = Field(...)
    category_id: UUID = Field(...)
    base_price: Decimal = Field(...)


class MenuItemResponse(MenuItemBase):
    id: UUID
    coefficient: Decimal
    final_price: Decimal
    category: CategoryResponse


class MenuItemUpdateResponse(MenuItemBase):
    id: UUID
    coefficient: Decimal
    final_price: Decimal


class MenuItemCreate(MenuItemBase):
    pass


class MenuItemInDB(MenuItemBase):
    coefficient: Decimal = Field(default=1.0)


class MenuItemUpdate(BaseModel):
    name: Optional[str] = None
    category_id: Optional[str] = None
    base_price: Optional[Decimal] = None
    coefficient: Optional[Decimal] = None


class MenuItemUpdateInDB(MenuItemUpdate):
    pass
