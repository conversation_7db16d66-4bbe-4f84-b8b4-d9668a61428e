import uuid
from datetime import datetime
from decimal import Decimal
from typing import Annotated

from sqlalchemy import UUID, ForeignKey, DateTime, func, Enum, Numeric

from .base import Base
from sqlalchemy.orm import Mapped, mapped_column, relationship

from models.enum import ChangeReason


class CoefficientLog(Base):
    __tablename__ = "coefficient_logs"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    item_id: Mapped[uuid.UUID] = mapped_column(ForeignKey("menu_items.id"), nullable=False)
    timestamp: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, default=func.now())
    previous_coefficient: Mapped[Decimal] = mapped_column(
        Numeric(10, 2),
        nullable=False,
    )
    new_coefficient: Mapped[Decimal] = mapped_column(
        Numeric(10, 2),
        nullable=False,
    )

    change_reason: Mapped[Annotated[ChangeReason, "change_reason"]] = mapped_column(Enum(ChangeReason),
                                                                                    default=ChangeReason.ORDERED,
                                                                                    nullable=False)
    menu_item: Mapped["MenuItem"] = relationship(back_populates="coefficient_logs")
