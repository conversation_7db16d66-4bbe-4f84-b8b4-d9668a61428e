from decimal import Decimal
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field

from schemas.menu_item import MenuItemCreate
from models.enum import Status
from schemas.order_item import OrderItemCreate, OrderItemResponse


class OrderBase(BaseModel):
    model_config = ConfigDict(from_attributes=True)


class OrderCreate(BaseModel):
    items: List[OrderItemCreate]


class OrderInDB(BaseModel):
    total_price: Decimal
    items: List[OrderItemCreate]


class OrderInDBUpdate(BaseModel):
    total_price: Optional[Decimal] = None


class OrderSimpleResponse(OrderBase):
    id: UUID
    total_price: Decimal
    status: Status


class OrderResponse(OrderBase):
    id: UUID
    total_price: Decimal
    status: Status
    items: List[OrderItemResponse]


class OrderUpdate(BaseModel):
    status: Optional[Status] = Field(None, description="New status for the order")

    model_config = ConfigDict(use_enum_values=True)
