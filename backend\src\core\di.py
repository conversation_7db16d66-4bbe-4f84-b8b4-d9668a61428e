from dependency_injector import containers, providers
from repository import MenuItemRepository, OrderItemRepository, OrderRepository, CoefficientLogRepository, \
    UserRepository, CategoryRepository
from services import MenuItemService, OrderService, CoefficientLogService, AuthService
from services.category_service import CategoryService

from .config import settings
from .database import Database


class Container(containers.DeclarativeContainer):
    wiring_config = containers.WiringConfiguration(
        modules=[
            "api.routes.menu_item",
            "api.routes.order",
            "api.routes.coefficient_log",
            "api.routes.auth",
            "api.routes.category",
            "api.deps"
        ]
    )

    database = providers.Singleton(Database, db_url=settings.async_database_url)

    user_repository = providers.Factory(UserRepository, session_factory=database.provided.session)
    menu_item_repository = providers.Factory(MenuItemRepository, session_factory=database.provided.session)
    category_repository = providers.Factory(CategoryRepository, session_factory=database.provided.session)
    order_repository = providers.Factory(OrderRepository, session_factory=database.provided.session)
    order_item_repository = providers.Factory(OrderItemRepository, session_factory=database.provided.session)
    coefficient_log_repository = providers.Factory(CoefficientLogRepository, session_factory=database.provided.session)

    auth_service = providers.Factory(AuthService, user_repository=user_repository)
    menu_item_service = providers.Factory(MenuItemService, menu_item_repository=menu_item_repository,
                                          coefficient_log_repository=coefficient_log_repository)
    category_service = providers.Factory(CategoryService, category_repository=category_repository)
    order_service = providers.Factory(OrderService, order_repository=order_repository,
                                      order_item_repository=order_item_repository,
                                      menu_item_repository=menu_item_repository,
                                      coefficient_log_repository=coefficient_log_repository)
    coefficient_log_service = providers.Factory(CoefficientLogService,
                                                coefficient_log_repository=coefficient_log_repository)
