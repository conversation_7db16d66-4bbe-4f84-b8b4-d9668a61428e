from datetime import datetime
from decimal import Decimal
from uuid import UUID

from pydantic import BaseModel, ConfigDict
from models.enum import ChangeReason
from schemas.menu_item import MenuItemResponse


class CoefficientLogBase(BaseModel):
    previous_coefficient: Decimal
    new_coefficient: Decimal
    change_reason: ChangeReason


class CoefficientLogInDB(CoefficientLogBase):
    item_id: UUID


class CoefficientLogResponse(CoefficientLogBase):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    item_id: UUID
    timestamp: datetime
    menu_item: MenuItemResponse
