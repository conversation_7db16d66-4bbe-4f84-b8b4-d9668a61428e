from uuid import UUID

from fastapi import HTTPException, status
from pydantic import BaseModel

from models import CoefficientLog
from repository.coefficient_log_repository import CoefficientLogRepository
from services.base_service import BaseService


class CoefficientLogService(BaseService[CoefficientLog, BaseModel, BaseModel, CoefficientLogRepository]):
    def __init__(self, coefficient_log_repository: CoefficientLogRepository):
        super().__init__(coefficient_log_repository)
        self._coefficient_log_repository = coefficient_log_repository

    async def get_all(self, item_id: UUID):
        try:
            return await self._coefficient_log_repository.get_all(item_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error"
            )
