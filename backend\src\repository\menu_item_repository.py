from sqlalchemy import <PERSON><PERSON><PERSON>, select, func, update
from sqlalchemy.exc import NoResultFound
from sqlalchemy.orm import selectinload

from models.menuitem import MenuItem
from schemas.menu_item import MenuItemCreate, MenuItemUpdate
from repository.base_repository import BaseRepository


class MenuItemRepository(BaseRepository[MenuItem, MenuItemCreate, MenuItemUpdate]):
    def __init__(self, session_factory):
        super().__init__(session_factory, MenuItem)

    async def soft_delete(self, id: UUID):
        async with self.session_factory() as session:
            async with session.begin():
                exists_query = select(self.model_class).where(self.model_class.id == id)
                result = await session.execute(exists_query)
                record = result.scalar_one_or_none()

                if record is None:
                    raise NoResultFound

                stmt = (
                    update(self.model_class)
                    .where(self.model_class.id == id)
                    .values(available=False, deleted_at=func.now())
                )
                await session.execute(stmt)

    async def get_available(self, pagination=None, category_id=None):
        async with self.session_factory() as session:
            stmt = (
                select(self.model_class)
                .where(self.model_class.deleted_at.is_(None))
                .options(selectinload(self.model_class.category))
            )

            if category_id:
                stmt = stmt.where(self.model_class.category_id == category_id)
            if pagination:
                stmt = stmt.offset(pagination.offset).limit(pagination.page_size)

            record = await session.execute(stmt)
            record = record.scalars().all()
            if record is None:
                raise NoResultFound

            return record

    async def get_available_by_id(self, id: UUID):
        async with self.session_factory() as session:
            stmt = (
                select(self.model_class)
                .where(
                    (self.model_class.deleted_at.is_(None))
                    & (self.model_class.id == id)
                )
                .options(selectinload(self.model_class.category))
            )
            record = await session.execute(stmt)
            record = record.scalars().first()
            if record is None:
                raise NoResultFound

            return record

    async def get_available_by_category_id(self, category_id: UUID):
        async with self.session_factory() as session:
            stmt = (
                select(self.model_class)
                .where(
                    (self.model_class.deleted_at.is_(None))
                    & (self.model_class.category_id == category_id)
                )
                .options(selectinload(self.model_class.category))
            )
            record = await session.execute(stmt)
            record = record.scalars().all()
            if record is None:
                raise NoResultFound

            return record

    async def count(self, category_id: UUID = None):
        async with self.session_factory() as session:
            stmt = select(func.count()).select_from(self.model_class).where(self.model_class.deleted_at.is_(None))

            if category_id:
                stmt = stmt.where(self.model_class.category_id == category_id)

            result = await session.execute(stmt)
            return result.scalar()
