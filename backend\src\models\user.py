import uuid
from uuid import <PERSON><PERSON><PERSON>

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Mapped, mapped_column

from .base import Base


class User(Base):
    __tablename__ = "users"

    id: Mapped[UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email: Mapped[str] = mapped_column(String, unique=True, index=True)
    hash_password: Mapped[str] = mapped_column(String)
    is_admin: Mapped[bool] = mapped_column(Boolean, default=False)
