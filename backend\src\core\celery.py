from celery import Celery
from celery.schedules import crontab

from .config import settings

celery_app = Celery(
    'tasks',
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
    include=['tasks.database_task']
)

celery_app.conf.beat_schedule = {
    'update-coefficient-hourly': {
        'task': 'tasks.database_task.update_item_coefficient',
        'schedule': crontab(minute=0),
    },
}

app = celery_app
