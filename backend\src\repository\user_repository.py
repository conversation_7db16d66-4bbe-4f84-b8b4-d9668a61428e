from pydantic import BaseModel
from sqlalchemy import select

from models.user import User
from repository.base_repository import BaseRepository


class UserRepository(BaseRepository[User, BaseModel, BaseModel]):
    def __init__(self, session_factory):
        super().__init__(session_factory, User)

    async def get_by_email(self, email: str):
        async with self.session_factory() as session:
            stmt = select(self.model_class).where(self.model_class.email == email)
            record = await session.execute(stmt)
            record = record.scalars().first()

            return record
