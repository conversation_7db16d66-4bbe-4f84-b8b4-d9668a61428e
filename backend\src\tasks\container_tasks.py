from dependency_injector import containers, providers

from core.database import Database
from repository import MenuItemRepository
from core.config import settings
from repository.coefficient_log_repository import CoefficientLogRepository


class TaskContainer(containers.DeclarativeContainer):
    database = providers.Singleton(Database, db_url=settings.async_database_url)

    menu_item_repository = providers.Factory(MenuItemRepository, session_factory=database.provided.session)
    coefficient_log_repository = providers.Factory(CoefficientLogRepository, session_factory=database.provided.session)
